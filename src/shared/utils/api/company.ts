import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';
import type { PaginateResponse } from '@shared/types/response';
import { companyEndPoints } from '@shared/utils/constants/servicesEndpoints';
import beforeCachePageDetail from '@shared/utils/normalizers/beforeCachePageDetail';
import type { CompanyType } from '@shared/types/company';
import request from '../toolkit/request';
import Endpoints from '../constants/endpoints';

export const getVendorsIncluded = async <T>(params?: {
  page?: number;
  size?: number;
}): Promise<PaginateResponse<T>> => {
  const { data } = await request.get<PaginateResponse<T>>(
    Endpoints.App.companyService.getVendorsIncluded,
    {
      params,
    }
  );
  return data;
};

export const getVendorsExcluded = async (id: string) => {
  const { data } = await request.get(
    Endpoints.App.companyService.getVendorsExcluded(id)
  );
  return data;
};

export const getSuggestCompany = async ({
  params,
}: {
  params?: {
    text: string;
    companyFilter: 'CLIENT' | 'VENDOR' | 'ALL';
    jobId: number;
  };
} = {}): Promise<SuggestSubmitToVendor[]> => {
  const { data } = await request.get(Endpoints.App.search.getSuggestCompany, {
    params,
  });

  return data;
};

export const postMultiJobSubmit = async (params: {
  vendorIds: string[];
  jobId: number;
  description: string;
}): Promise<any> => {
  const { data } = await request.post(
    Endpoints.App.companyService.postMultiJobSubmit,
    {
      ...params,
    }
  );
  return data;
};

export const addVendors = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addVendors, params);
  return data;
};
export const addVendor = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addVendor, params);
  return data;
};

export const addClients = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addClients, params);
  return data;
};

export const addClient = async (params: any): Promise<any> => {
  const { data } = await request.post(companyEndPoints.addClient, params);
  return data;
};

export const acceptRequest = async ({ id = '' }: { id?: string }) => {
  await request.put(companyEndPoints.acceptRequest(id));
};

export const declineRequest = async ({ id = '' }: { id?: string }) => {
  await request.put(companyEndPoints.delcineRequest(id));
};

export const getCompanyDetailByPageId = async ({
  params,
  accessToken,
}: {
  params: any;
  accessToken?: string;
}): Promise<CompanyType> => {
  if (!params.pageId) {
    throw new Error('pageId is required');
  }
  const { data } = await request.get(
    companyEndPoints.getCompanyDetailByPageId(params.pageId),
    {
      accessToken,
      params,
    }
  );

  return {
    ...beforeCachePageDetail(data),
    // is
    id: data.pageId,
    vendorClientId: data.id,
  };
};

export const getPendingAndRequestCount = async (id: string) => {
  const { data } = await request.get(
    companyEndPoints.getPendingAndRequestCount
  );
  return data;
};

export const cancelRequest = async ({ id }) => {
  console.log({ id });
  await request.put(companyEndPoints.cancelRequest(id));
};
