/* eslint-disable jsx-a11y/click-events-have-key-events */
import type { ReactNode } from 'react';
import React, { useEffect, useState, useCallback, useRef, memo } from 'react';
import { Virtualizer, type VirtualizerHandle } from 'virtua';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import Flex from '../Flex';
import cnj from '../utils/cnj';

type GeneralItem = {
  id: string;
};

export interface SearchableAsyncListProps<T extends GeneralItem = GeneralItem> {
  name: string;
  value?: T | T[];
  variant: 'single' | 'multi';
  renderItem: (args: {
    item: T;
    isSelected: boolean;
    index: number;
    text?: string;
  }) => React.ReactNode;
  url?: string;
  apiFunc?: (params: any) => Promise<any>;
  normalizer?: (data: any) => T[];
  keywords?: string;
  params?: Record<string, any>;
  initSearchValue?: string;
  hardRefetch?: boolean;
  accessToken?: string;
  onChange?: (value: T | T[] | undefined) => void;
  onChangeInput?: (input: string) => void;
  onOptions?: (options: T[]) => void;
  placeholder?: string;
  limit?: number;
  listItemClassName?: string;
  wrapperClassName?: string;
  searchClassName?: string;
  listItemsClassName?: string;
  hasSearch?: boolean;
  renderInfoMessage?: ReactNode;
  renderLoading?: ReactNode;
  renderEmpty?: ReactNode;
  renderNextPageLoading?: ReactNode;
  renderError?: ReactNode;
  scrollRef?: any;
  startMargin?: number;
  pageSize?: number;
  enableInfiniteScroll?: boolean;
}

const SearchableAsyncList = <T extends GeneralItem = GeneralItem>({
  name,
  value: parentValue,
  variant,
  renderItem,
  url,
  normalizer,
  onChange: parentOnChange,
  onChangeInput: parentOnChangeInput,
  apiFunc,
  onOptions,
  accessToken,
  renderInfoMessage,
  renderLoading,
  renderEmpty,
  renderNextPageLoading,
  renderError,
  listItemClassName,
  listItemsClassName,
  searchClassName,
  wrapperClassName,
  scrollRef,
  startMargin = 0,
  pageSize = 20,
  enableInfiniteScroll = true,
  params = {},
  hasSearch = true,
  keywords = 'text',
  hardRefetch = true,
  initSearchValue = '',
  placeholder = 'Search...',
  limit = variant === 'multi' ? 10 : 3,
}: SearchableAsyncListProps<T>) => {
  const [inputValue, setInputValue] = useState('');
  const [selectedItems, setSelectedItems] = useState<T[]>(() => {
    if (variant === 'multi') {
      return Array.isArray(parentValue) ? parentValue : [];
    }
    return parentValue ? [parentValue as T] : [];
  });

  const { debounceValue, setValue } = useDebounceState<string>(
    initSearchValue,
    500
  );

  const queryKey = [name, debounceValue, JSON.stringify(params)].filter(
    Boolean
  ) as string[];
  const virtRef = useRef<VirtualizerHandle>(null);
  const refetchCalled = useRef(false);

  // Create a wrapper function that formats parameters correctly for the backend
  const wrappedApiFunc = useCallback(
    (queryParams: any) => {
      if (!apiFunc) return Promise.resolve({ content: [], last: true });

      // Extract page from useInfiniteQuery (starts from undefined, then 1, 2, 3...)
      // Convert to 1-based indexing for backend (useInfiniteQuery uses 0-based)
      const page = (queryParams.page || 0) + 1;

      // Format parameters as expected by the backend
      const formattedParams = {
        params: {
          [keywords]: debounceValue,
          ...params,
          page,
          size: pageSize,
        },
      };

      console.log('API call with params:', formattedParams);

      return apiFunc(formattedParams).then((response: any) => {
        console.log('API response:', response);
        return response;
      });
    },
    [apiFunc, keywords, debounceValue, params, pageSize]
  );

  const {
    data: filteredOptions = [],
    fetchNextPage,
    isFetchingNextPage,
    status,
    hasNextPage,
    refetch,
    error,
  } = useInfiniteQuery(
    queryKey,
    {
      func: wrappedApiFunc,
      size: pageSize,
    },
    {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      onSuccess: (data: any) => {
        const newItems = normalizer ? normalizer(data) : data;
        onOptions?.(newItems || []);
      },
      onError: (e: any) => {
        if (
          e?.response?.data?.error === 'ZeroPageIsNotCalledException' &&
          !refetchCalled.current
        ) {
          refetchCalled.current = true;
          refetch();
        }
      },
      getNextPageParam: (lastPage: any) => {
        // Check if there are more pages available
        // The backend should return a 'last' field or we can check if we got fewer items than requested
        if (lastPage?.last === false || (lastPage?.content && lastPage.content.length === pageSize)) {
          return (lastPage?.number || 0) + 1;
        }
        return undefined;
      },
    }
  );

  // Data manipulation hooks for potential future use
  // const { add, remove, replace } = useUpdateInfinityData(queryKey);
  const isLoading = status === 'loading';

  // Infinite scroll logic
  const onScroll = useCallback(() => {
    if (!enableInfiniteScroll) return;

    const handle = virtRef.current;
    if (!handle) return;

    const endIndex = handle.findEndIndex();
    console.log('Scroll detected:', {
      endIndex,
      totalItems: filteredOptions.length,
      hasNextPage,
      isFetchingNextPage,
      shouldFetch: hasNextPage && !isFetchingNextPage && endIndex >= filteredOptions.length - 6
    });

    if (
      hasNextPage &&
      !isFetchingNextPage &&
      endIndex >= filteredOptions.length - 6
    ) {
      console.log('Fetching next page...');
      fetchNextPage();
    }
  }, [
    enableInfiniteScroll,
    hasNextPage,
    isFetchingNextPage,
    filteredOptions.length,
    fetchNextPage,
  ]);

  // Alternative scroll detection for non-virtualized mode
  const onScrollFallback = useCallback((e: any) => {
    if (!enableInfiniteScroll) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

    console.log('Fallback scroll detected:', {
      scrollTop,
      scrollHeight,
      clientHeight,
      isNearBottom,
      hasNextPage,
      isFetchingNextPage
    });

    if (isNearBottom && hasNextPage && !isFetchingNextPage) {
      console.log('Fetching next page (fallback)...');
      fetchNextPage();
    }
  }, [enableInfiniteScroll, hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    if (debounceValue || hardRefetch) {
      refetch();
    }
  }, [debounceValue, hardRefetch, refetch]);

  // Debug effect to monitor infinite query state
  useEffect(() => {
    console.log('Infinite query state:', {
      status,
      hasNextPage,
      isFetchingNextPage,
      totalItems: filteredOptions.length,
      enableInfiniteScroll
    });
  }, [status, hasNextPage, isFetchingNextPage, filteredOptions.length, enableInfiniteScroll]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement> | string) => {
      const { value } = typeof e === 'string' ? { value: e } : e.target;
      setInputValue(value);
      setValue(value);
      parentOnChangeInput?.(value);
    },
    [setValue, parentOnChangeInput]
  );

  const handleInputFocus = useCallback(() => {
    if (!filteredOptions.length) {
      refetch();
    }
  }, [filteredOptions.length, refetch]);

  const handleItemSelect = useCallback(
    (item: T) => {
      if (variant === 'single') {
        setSelectedItems([item]);
        parentOnChange?.(item);
      } else {
        const isAlreadySelected = selectedItems.some(
          (selected) => selected.id === item.id
        );

        let newSelection: T[];
        if (isAlreadySelected) {
          newSelection = selectedItems.filter(
            (selected) => selected.id !== item.id
          );
        } else if (selectedItems.length < limit) {
          newSelection = [...selectedItems, item];
        } else {
          return;
        }

        setSelectedItems(newSelection);
        parentOnChange?.(newSelection);
      }
    },
    [variant, selectedItems, limit, parentOnChange]
  );

  const isItemSelected = useCallback(
    (item: T) => selectedItems.some((selected) => selected.id === item.id),
    [selectedItems]
  );

  // Handle initial loading state
  if (isLoading) {
    return (
      <Flex flexDir="column" className={wrapperClassName}>
        {hasSearch && (
          <SearchInputV2
            placeholder={placeholder}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            className={cnj('w-full', searchClassName)}
            value={inputValue}
          />
        )}
        {renderLoading && renderLoading}
      </Flex>
    );
  }

  // Handle error state
  if (error && renderError) {
    return (
      <Flex flexDir="column" className={wrapperClassName}>
        {hasSearch && (
          <SearchInputV2
            placeholder={placeholder}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            className={cnj('w-full', searchClassName)}
            value={inputValue}
          />
        )}
        {renderError}
      </Flex>
    );
  }

  return (
    <Flex flexDir="column" className={wrapperClassName}>
      {hasSearch && (
        <SearchInputV2
          placeholder={placeholder}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          className={cnj('w-full', searchClassName)}
          value={inputValue}
        />
      )}

      {!!renderInfoMessage && renderInfoMessage}

      {filteredOptions?.length ? (
        <Flex flexDir="column" className={listItemsClassName}>
          {enableInfiniteScroll ? (
            <Virtualizer
              ref={virtRef}
              scrollRef={scrollRef}
              startMargin={startMargin}
              onScroll={onScroll}
            >
              {filteredOptions.map((item, index) => (
                <div
                  key={item.id}
                  role="option"
                  aria-selected={isItemSelected(item)}
                  tabIndex={0}
                  onClick={() => handleItemSelect(item)}
                  className={cnj('cursor-pointer', listItemClassName)}
                >
                  {renderItem({
                    item,
                    isSelected: isItemSelected(item),
                    index,
                    text: inputValue,
                  })}
                </div>
              ))}
            </Virtualizer>
          ) : (
            <Flex
              flexDir="column"
              className={cnj('overflow-y-auto', listItemsClassName)}
              onScroll={onScrollFallback}
            >
              {filteredOptions.map((item, index) => (
                <div
                  key={item.id}
                  role="option"
                  aria-selected={isItemSelected(item)}
                  tabIndex={0}
                  onClick={() => handleItemSelect(item)}
                  className={cnj('cursor-pointer', listItemClassName)}
                >
                  {renderItem({
                    item,
                    isSelected: isItemSelected(item),
                    index,
                    text: inputValue,
                  })}
                </div>
              ))}
            </Flex>
          )}
          {isFetchingNextPage && renderNextPageLoading && renderNextPageLoading}
        </Flex>
      ) : (
        !isLoading && renderEmpty && renderEmpty
      )}
    </Flex>
  );
};

export default memo(SearchableAsyncList);
